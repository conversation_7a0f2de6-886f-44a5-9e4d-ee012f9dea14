import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final apiConfigController = Get.find<ApiConfigController>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  title: const Text('API配置'),
                  trailing: IconButton(
                    icon: const Icon(Icons.swap_horiz),
                    onPressed: () {
                      apiConfigController.toggleConfigMode();
                      Get.snackbar(
                        '提示', 
                        '已切换到${apiConfigController.isTextToSpeechMode.value ? "文本转语音" : "AI对话"}配置',
                      );
                    },
                  ),
                ),
                Obx(() {
                  if (apiConfigController.isTextToSpeechMode.value) {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '文本转语音API配置',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: TextEditingController(
                              text: apiConfigController.ttsApiKey.value,
                            ),
                            decoration: const InputDecoration(
                              labelText: 'API密钥',
                              hintText: '请输入硅基流动API密钥',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: apiConfigController.setTTSApiKey,
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'AI对话API配置',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: TextEditingController(
                              text: apiConfigController.apiKey.value,
                            ),
                            decoration: const InputDecoration(
                              labelText: 'API密钥',
                              hintText: '请输入API密钥',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: apiConfigController.setApiKey,
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: TextEditingController(
                              text: apiConfigController.baseUrl.value,
                            ),
                            decoration: const InputDecoration(
                              labelText: '基础URL',
                              hintText: '请输入基础URL',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: apiConfigController.setBaseUrl,
                          ),
                        ],
                      ),
                    );
                  }
                }),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: ListTile(
              leading: const Icon(Icons.delete_sweep_outlined, color: Colors.red),
              title: const Text('清除应用缓存'),
              subtitle: const Text('清除所有本地缓存和会话数据'),
              onTap: () {
                Get.dialog(
                  AlertDialog(
                    title: const Text('确认清除缓存？'),
                    content: const Text('这将清除所有本地保存的小说生成历史、会话数据和其他缓存。此操作不可恢复。'),
                    actions: [
                      TextButton(
                        child: const Text('取消'),
                        onPressed: () => Get.back(),
                      ),
                      TextButton(
                        child: const Text('确认清除', style: TextStyle(color: Colors.red)),
                        onPressed: () async {
                          Get.back();
                          final novelController = Get.find<NovelController>();
                          await novelController.clearAllApplicationCache();
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
} 